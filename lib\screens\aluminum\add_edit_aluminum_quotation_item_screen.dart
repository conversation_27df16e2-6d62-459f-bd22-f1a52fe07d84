import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../../models/aluminum_quotation.dart';
import '../../models/aluminum_quotation_item.dart';
import '../../models/aluminum_profile.dart';
import '../../models/profile_series.dart';
import '../../services/unified_aluminum_service.dart';

class AddEditAluminumQuotationItemScreen extends StatefulWidget {
  final AluminumQuotation quotation;
  final AluminumQuotationItem? item;

  const AddEditAluminumQuotationItemScreen({
    super.key,
    required this.quotation,
    this.item,
  });

  @override
  State<AddEditAluminumQuotationItemScreen> createState() => _AddEditAluminumQuotationItemScreenState();
}

class _AddEditAluminumQuotationItemScreenState extends State<AddEditAluminumQuotationItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  // Controllers
  final TextEditingController _widthController = TextEditingController();
  final TextEditingController _heightController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // State variables
  WindowDoorType _selectedType = WindowDoorType.hinge; // افتراضي مفصلي
  SashCount _selectedSashCount = SashCount.one; // افتراضي ضلفة واحدة
  TrackCount? _selectedTrackCount; // للسحاب فقط
  ProfileSeries? _selectedSeries;
  List<ProfileSeries> _availableSeries = [];
  final Map<ProfileCategory, AluminumProfile?> _selectedProfiles = {};
  List<AluminumProfile> _availableProfiles = [];
  bool _isLoading = false;
  bool _isLoadingProfiles = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadSeries();
  }

  @override
  void dispose() {
    _widthController.dispose();
    _heightController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeData() {
    if (widget.item != null) {
      final item = widget.item!;
      _selectedType = item.type;
      _selectedSashCount = item.sashCount;
      _selectedTrackCount = item.trackCount;
      _widthController.text = item.width.toString();
      _heightController.text = item.height.toString();
      _quantityController.text = item.quantity.toString();
      _notesController.text = item.notes;
    } else {
      _quantityController.text = '1';
      // تعيين القيم الافتراضية حسب النوع
      _updateDefaultValues();
    }
  }

  void _updateDefaultValues() {
    if (_selectedType == WindowDoorType.hinge) {
      _selectedSashCount = SashCount.one;
      _selectedTrackCount = null;
    } else {
      _selectedSashCount = SashCount.two;
      _selectedTrackCount = TrackCount.one;
    }
  }

  Future<void> _loadSeries() async {
    setState(() => _isLoading = true);
    try {

      final seriesData = await _aluminumService.getAllProfileSeries();
      final series = seriesData.map((data) => ProfileSeries.fromMap(data)).toList();
      setState(() {
        _availableSeries = series;
        _isLoading = false;

        // إذا كان هناك بند موجود، حاول العثور على المجموعة المحددة
        if (widget.item?.seriesId != null) {
          try {
            _selectedSeries = series.firstWhere(
              (s) => s.id == widget.item!.seriesId,
            );
          } catch (e) {
            _selectedSeries = series.isNotEmpty ? series.first : null;
          }
          if (_selectedSeries != null) {
            _loadProfiles();
          }
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المجموعات: $e')),
        );
      }
    }
  }

  Future<void> _loadProfiles() async {
    if (_selectedSeries == null) return;

    setState(() => _isLoadingProfiles = true);
    try {
      final profilesData = await _aluminumService.getProfilesBySeries(_selectedSeries!.id!);
      final profiles = profilesData.map((data) => AluminumProfile.fromMap(data)).toList();
      setState(() {
        _availableProfiles = profiles;
        _isLoadingProfiles = false;

        // إعادة تعيين القطاعات المختارة
        _selectedProfiles.clear();
      });
    } catch (e) {
      setState(() => _isLoadingProfiles = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل القطاعات: $e')),
        );
      }
    }
  }

  void _onTypeChanged(WindowDoorType? newType) {
    if (newType != null && newType != _selectedType) {
      setState(() {
        _selectedType = newType;
        _selectedSeries = null;
        _selectedProfiles.clear();
        _availableProfiles.clear();
        _updateDefaultValues();
      });
      _loadSeries();
    }
  }

  void _onSeriesChanged(ProfileSeries? newSeries) {
    if (newSeries != null && newSeries != _selectedSeries) {
      setState(() {
        _selectedSeries = newSeries;
        _selectedProfiles.clear();
      });
      _loadProfiles();
    }
  }

  List<ProfileCategory> _getAvailableCategories() {
    return _selectedType == WindowDoorType.hinge
        ? ProfileCategory.getHingeCategories()
        : ProfileCategory.getSlidingCategories();
  }

  List<AluminumProfile> _getProfilesByCategory(ProfileCategory category) {
    return _availableProfiles.where((p) => p.category == category).toList();
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    final width = double.tryParse(_widthController.text);
    final height = double.tryParse(_heightController.text);
    final quantity = int.tryParse(_quantityController.text);

    if (width == null || height == null || quantity == null ||
        width <= 0 || height <= 0 || quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال قيم صحيحة')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();
      // تحويل نوع الفتحة إلى نوع القطاع
      final profileType = _selectedType == WindowDoorType.hinge
          ? ProfileType.hinge
          : ProfileType.sliding;

      final newItem = AluminumQuotationItem(
        id: widget.item?.id,
        quotationId: widget.quotation.id!,
        type: _selectedType,
        profileType: profileType,
        seriesId: _selectedSeries?.id,
        seriesName: _selectedSeries?.name,
        sashCount: _selectedSashCount,
        trackCount: _selectedTrackCount,
        width: width,
        height: height,
        quantity: quantity,
        notes: _notesController.text.trim(),
        createdAt: widget.item?.createdAt ?? now,
      );

      if (widget.item == null) {
        await _aluminumService.insertQuotationItem(newItem);
      } else {
        await _aluminumService.updateQuotationItem(newItem);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.item == null ? 'تم إضافة البند بنجاح' : 'تم تحديث البند بنجاح'),
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ البند: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.item == null ? 'إضافة بند جديد' : 'تعديل البند'),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildBasicInfoCard(),
              const SizedBox(height: 16),
              _buildProfileSelectionCard(),
              const SizedBox(height: 16),
              _buildProfileCategoriesCard(),
              const SizedBox(height: 24),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات البند الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: const Color(0xFF607D8B),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // نوع الفتحة (مفصلي/سحاب)
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.all(12),
                          child: Text(
                            'نوع الفتحة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: RadioListTile<WindowDoorType>(
                                title: const Text('مفصلي'),
                                value: WindowDoorType.hinge,
                                groupValue: _selectedType,
                                onChanged: _onTypeChanged,
                                dense: true,
                              ),
                            ),
                            Expanded(
                              child: RadioListTile<WindowDoorType>(
                                title: const Text('سحاب'),
                                value: WindowDoorType.sliding,
                                groupValue: _selectedType,
                                onChanged: _onTypeChanged,
                                dense: true,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // عدد الضلف
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<SashCount>(
                    value: _selectedSashCount,
                    decoration: const InputDecoration(
                      labelText: 'عدد الضلف',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.view_column),
                    ),
                    items: (_selectedType == WindowDoorType.hinge
                        ? SashCount.getHingeOptions()
                        : SashCount.getSlidingOptions()).map((count) {
                      return DropdownMenuItem(
                        value: count,
                        child: Text(count.arabicName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSashCount = value;
                          // إعادة تعيين عدد السكك إذا لزم الأمر
                          if (_selectedType == WindowDoorType.sliding && value.count <= 2) {
                            _selectedTrackCount = TrackCount.one;
                          }
                        });
                      }
                    },
                  ),
                ),
              ],
            ),

            // عدد السكك (للسحاب فقط عند أكثر من ضلفتين)
            if (_selectedType == WindowDoorType.sliding && _selectedSashCount.count > 2) ...[
              const SizedBox(height: 16),
              DropdownButtonFormField<TrackCount>(
                value: _selectedTrackCount,
                decoration: const InputDecoration(
                  labelText: 'الحلق كام سكة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.linear_scale),
                ),
                items: TrackCount.values.map((track) {
                  return DropdownMenuItem(
                    value: track,
                    child: Text(track.arabicName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedTrackCount = value;
                  });
                },
              ),
            ],
            const SizedBox(height: 16),

            // الأبعاد والكمية
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _widthController,
                    decoration: const InputDecoration(
                      labelText: 'العرض (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final width = double.tryParse(value);
                      if (width == null || width <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _heightController,
                    decoration: const InputDecoration(
                      labelText: 'الارتفاع (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.height),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final height = double.tryParse(value);
                      if (height == null || height <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final quantity = int.tryParse(value);
                      if (quantity == null || quantity <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // ملاحظات
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار مجموعة القطاعات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: const Color(0xFF607D8B),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_availableSeries.isEmpty)
              const Center(
                child: Text(
                  'لا توجد مجموعات متاحة',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              DropdownButtonFormField<ProfileSeries>(
                value: _selectedSeries,
                decoration: const InputDecoration(
                  labelText: 'اختر المجموعة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: _availableSeries.map((series) {
                  return DropdownMenuItem(
                    value: series,
                    child: Text('${series.name} (${series.code})'),
                  );
                }).toList(),
                onChanged: _onSeriesChanged,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCategoriesCard() {
    if (_selectedSeries == null || _availableProfiles.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: const Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text(
              'يرجى اختيار مجموعة القطاعات أولاً',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار القطاعات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: const Color(0xFF607D8B),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (_isLoadingProfiles)
              const Center(child: CircularProgressIndicator())
            else
              ..._getAvailableCategories().map((category) {
                final profiles = _getProfilesByCategory(category);
                if (profiles.isEmpty) return const SizedBox.shrink();

                return _buildCategorySection(category, profiles);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection(ProfileCategory category, List<AluminumProfile> profiles) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            category.arabicName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF607D8B),
            ),
          ),
        ),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: profiles.length,
            itemBuilder: (context, index) {
              final profile = profiles[index];
              final isSelected = _selectedProfiles[category]?.id == profile.id;

              return _buildProfileCard(profile, isSelected, () {
                setState(() {
                  _selectedProfiles[category] = isSelected ? null : profile;
                });
              });
            },
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildProfileCard(AluminumProfile profile, bool isSelected, VoidCallback onTap) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? const Color(0xFF607D8B) : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? const Color(0xFF607D8B).withValues(alpha: 0.1) : Colors.white,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة القطاع
              Container(
                width: 60,
                height: 40,
                decoration: BoxDecoration(
                  color: _getProfileCategoryColor(profile.category).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: _getProfileCategoryColor(profile.category).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: profile.imagePath != null && profile.imagePath!.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: Image.memory(
                                  Uint8List.fromList(profile.imagePath!.codeUnits),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              _getProfileCategoryIcon(profile.category),
                              color: _getProfileCategoryColor(profile.category),
                              size: 24,
                            );
                          },
                        ),
                      )
                    : Icon(
                        _getProfileCategoryIcon(profile.category),
                        color: _getProfileCategoryColor(profile.category),
                        size: 24,
                      ),
              ),
              const SizedBox(height: 4),

              // كود القطاع
              Text(
                profile.code,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? const Color(0xFF607D8B) : Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // اسم القطاع
              Text(
                profile.name,
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected ? const Color(0xFF607D8B) : Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveItem,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF607D8B),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                widget.item == null ? 'إضافة البند' : 'تحديث البند',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  // دالة للحصول على أيقونة فئة القطاع
  IconData _getProfileCategoryIcon(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Icons.crop_square;
      case ProfileCategory.bar:
        return Icons.horizontal_rule;
      case ProfileCategory.dalfa:
        return Icons.view_column;
      case ProfileCategory.marad:
        return Icons.more_vert;
      case ProfileCategory.baketa:
        return Icons.crop_din;
      case ProfileCategory.soas:
        return Icons.linear_scale;
      case ProfileCategory.dalfaSilk:
        return Icons.grid_view;
      case ProfileCategory.olba:
        return Icons.crop_7_5;
      case ProfileCategory.filta:
        return Icons.crop_16_9;
      case ProfileCategory.skineh:
        return Icons.view_stream;
      case ProfileCategory.anf:
        return Icons.crop_landscape;
    }
  }

  // دالة للحصول على لون فئة القطاع
  Color _getProfileCategoryColor(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Colors.blue;
      case ProfileCategory.bar:
        return Colors.green;
      case ProfileCategory.dalfa:
        return Colors.orange;
      case ProfileCategory.marad:
        return Colors.purple;
      case ProfileCategory.baketa:
        return Colors.red;
      case ProfileCategory.soas:
        return Colors.teal;
      case ProfileCategory.dalfaSilk:
        return Colors.indigo;
      case ProfileCategory.olba:
        return Colors.brown;
      case ProfileCategory.filta:
        return Colors.pink;
      case ProfileCategory.skineh:
        return Colors.cyan;
      case ProfileCategory.anf:
        return Colors.amber;
    }
  }
}